# AWS SSO Setup Guide

This guide will help you set up AWS SSO for the SOW Reviewer Infrastructure deployment.

## Current Available Profiles

Based on your current AWS configuration, you have these profiles available:

```
default          - Basic profile (deprecated - use SSO instead)
cenergistic      - Cross-account role via SSO
ce-test-1        - <PERSON><PERSON> profile for account ************
ce-test-5        - SS<PERSON> profile for account ************  
protagona        - SSO profile for account ************
hf               - Cross-account role via protagona
pr               - SSO profile for account ************
```

## Recommended Profiles for SOW Reviewer

For deploying the SOW Reviewer infrastructure, we recommend using one of these profiles:

### Option 1: ce-test-1 (Testing Account)
- **Account ID**: ************
- **Role**: AWSAdministratorAccess
- **Good for**: Development and testing

### Option 2: protagona (Main Account) 
- **Account ID**: ************
- **Role**: AWSPowerUserAccess
- **Good for**: Development (limited permissions)

### Option 3: pr (Alternative Account)
- **Account ID**: ************  
- **Role**: AWSPowerUserAccess
- **Good for**: Development

## Quick Start Commands

### 1. Choose your profile and test SSO login:

```powershell
# Test SSO login with ce-test-1 (recommended for dev)
aws sso login --profile ce-test-1

# Verify login worked
aws sts get-caller-identity --profile ce-test-1
```

### 2. Deploy with the chosen profile:

```powershell
# Plan deployment
.\deploy.ps1 -Environment dev -Action plan -Profile ce-test-1

# Apply deployment
.\deploy.ps1 -Environment dev -Action apply -Profile ce-test-1
```

## SSO Session Management

### Check current SSO status:
```powershell
aws sts get-caller-identity --profile ce-test-1
```

### Manual SSO login if needed:
```powershell
aws sso login --profile ce-test-1
```

### Logout from SSO:
```powershell
aws sso logout
```

## Profile Configuration Details

Your profiles are configured with these SSO settings:

- **SSO Start URL**: https://protagona.awsapps.com/start#/
- **SSO Region**: us-east-1
- **Default Region**: us-east-1
- **Output Format**: json

## Troubleshooting

### Issue: "SSO session expired"
**Solution**: Run the deployment script again - it will automatically trigger SSO login

### Issue: "Profile not found"
**Solution**: Check available profiles with `aws configure list-profiles`

### Issue: "Access denied"
**Solution**: Ensure you're using a profile with sufficient permissions (Administrator or PowerUser access)

### Issue: "Bedrock not available"
**Solution**: 
1. Ensure Bedrock service is enabled in us-east-1
2. Request Claude model access in the AWS console
3. Verify your profile has Bedrock permissions

## Environment-Specific Profile Recommendations

- **Development**: `ce-test-1` (Full admin access for testing)
- **Staging**: `protagona` (PowerUser access, more controlled)  
- **Production**: Create dedicated production profile with minimal required permissions

## Next Steps

1. Choose your preferred profile from the list above
2. Test SSO login: `aws sso login --profile your-chosen-profile`
3. Run deployment: `.\deploy.ps1 -Environment dev -Action plan -Profile your-chosen-profile`

The deployment script will handle SSO session management automatically!
