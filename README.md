# SOW Reviewer Infrastructure

This repository contains Terraform infrastructure code for the SOW (Statement of Work) Reviewer application. The infrastructure is modular and supports multiple environments using Terraform workspaces.

## Architecture Overview

The SOW Reviewer system consists of the following components:

### Core Services
- **API Gateway**: Async REST API with CORS support
- **Lambda Functions**: Four specialized functions for different operations
- **S3 Buckets**: Document storage and review output storage
- **DynamoDB Tables**: Document status tracking and processing results
- **Bedrock Agents**: Three specialized AI agents for different review types

### API Endpoints

The system provides the following REST endpoints:

1. **POST /upload** - Upload SOW documents
2. **POST /process** - Invoke agents to process documents
3. **GET /status/{document_id}** - Check document processing status
4. **GET /documents** - List documents by status (with optional filtering)

### Bedrock Agents

Three specialized agents for different review perspectives:
- **Technical Agent**: Reviews technical feasibility and implementation details
- **Financial Agent**: Reviews financial aspects and funding requirements
- **Legal Agent**: Reviews legal implications and contract language

## Directory Structure

```
.
├── main.tf                 # Main configuration using modules
├── variables.tf            # Input variables and validation
├── outputs.tf              # Output values
├── providers.tf            # Provider configuration
├── data.tf                 # Data sources (legacy)
├── environments/           # Environment-specific configurations
│   ├── dev.tfvars
│   ├── staging.tfvars
│   └── prod.tfvars
└── modules/                # Terraform modules
    ├── api-gateway/        # API Gateway configuration
    ├── bedrock-agents/     # Bedrock agents setup
    ├── dynamodb/           # DynamoDB tables
    ├── lambda/             # Lambda functions
    └── s3/                 # S3 buckets
```

## Prerequisites

1. **AWS CLI** configured with appropriate credentials
2. **Terraform** >= 1.0
3. **AWS Account** with:
   - Bedrock access enabled
   - Claude model access approved
   - Appropriate IAM permissions

## Deployment

### Quick Start with AWS SSO (Recommended)

Use the provided PowerShell deployment script for Windows with AWS SSO:

```powershell
# List available AWS profiles
aws configure list-profiles

# Plan deployment with SSO profile
.\deploy.ps1 -Environment dev -Action plan -Profile your-sso-profile

# Apply deployment with SSO profile (will auto-login if needed)
.\deploy.ps1 -Environment dev -Action apply -Profile your-sso-profile

# Apply with auto-approval (for automation)
.\deploy.ps1 -Environment dev -Action apply -Profile your-sso-profile -AutoApprove

# Destroy infrastructure
.\deploy.ps1 -Environment dev -Action destroy -Profile your-sso-profile
```

**Note**: The script will automatically handle SSO login if your session has expired.

### Manual Deployment

If you prefer manual Terraform commands:

```bash
# 1. Initialize Terraform
terraform init

# 2. Create and select workspace
terraform workspace new dev
# or select existing: terraform workspace select dev

# 3. Plan deployment
terraform plan -var-file="environments/dev.tfvars"

# 4. Apply configuration
terraform apply -var-file="environments/dev.tfvars"
```

### Environment-Specific Deployment

For different environments:

```bash
# Development
terraform workspace select dev
terraform apply -var-file="environments/dev.tfvars"

# Staging
terraform workspace select staging
terraform apply -var-file="environments/staging.tfvars"

# Production
terraform workspace select prod
terraform apply -var-file="environments/prod.tfvars"
```

## Usage

### API Usage Examples

#### 1. Upload a Document

```bash
curl -X POST https://your-api-gateway-url/dev/upload \
  -H "Content-Type: application/json" \
  -d '{
    "file_name": "sow_example.txt",
    "file_content": "base64-encoded-file-content",
    "content_type": "text/plain"
  }'
```

Response:
```json
{
  "document_id": "uuid-here",
  "s3_uri": "s3://bucket-name/documents/uuid/filename.txt",
  "message": "File uploaded successfully"
}
```

#### 2. Process Document with Agent

```bash
curl -X POST https://your-api-gateway-url/dev/process \
  -H "Content-Type: application/json" \
  -d '{
    "document_id": "document-uuid",
    "agent_type": "technical"
  }'
```

Agent types: `technical`, `financial`, `legal`

#### 3. Check Document Status

```bash
curl -X GET https://your-api-gateway-url/dev/status/document-uuid
```

#### 4. List Unprocessed Documents

```bash
# List all uploaded documents
curl -X GET https://your-api-gateway-url/dev/documents

# Filter by status
curl -X GET https://your-api-gateway-url/dev/documents?status=uploaded&limit=10
```

## Configuration

### Variables

Key variables in `variables.tf`:

- `environment`: Environment name (dev/staging/prod)
- `region`: AWS region for deployment
- `foundation_model_arn`: Bedrock model ARN (optional, defaults per environment)
- `default_tags`: Default tags applied to all resources

### Environment-Specific Settings

Each environment has its own `.tfvars` file in the `environments/` directory with specific configurations for tags, resource sizing, and other environment-specific parameters.

## Modules

### S3 Module (`modules/s3/`)
- Creates document storage bucket with versioning and encryption
- Creates review output bucket with versioning and encryption
- Configures CORS for web uploads

### DynamoDB Module (`modules/dynamodb/`)
- Document status tracking table with GSI for status queries
- Processing results table for storing agent outputs
- Pay-per-request billing for cost optimization

### Bedrock Agents Module (`modules/bedrock-agents/`)
- Three specialized agents with environment-specific naming
- IAM roles and policies for agent execution
- Agent aliases for version management

### Lambda Module (`modules/lambda/`)
- Four Lambda functions with different responsibilities
- Shared IAM role with comprehensive permissions
- Environment variables for resource references

### API Gateway Module (`modules/api-gateway/`)
- REST API with CORS support
- Four main endpoints with proper routing
- Lambda integrations and permissions

## Security

- All S3 buckets use AES256 encryption
- Lambda functions run with least-privilege IAM roles
- API Gateway endpoints are public but can be secured with API keys
- DynamoDB tables use AWS managed encryption
- Bedrock agents have scoped permissions

## Monitoring and Logging

- All Lambda functions include CloudWatch logging
- API Gateway has access logging available
- Resource tagging for cost allocation and monitoring
- Environment-specific naming for resource identification

## Costs

The infrastructure uses serverless and pay-per-use services:
- Lambda: Pay per invocation and duration
- DynamoDB: Pay-per-request billing
- S3: Pay for storage and requests
- API Gateway: Pay per API call
- Bedrock: Pay per model invocation

## Troubleshooting

### Common Issues

1. **Bedrock Access**: Ensure Bedrock service is enabled in your region
2. **Model Access**: Verify Claude model access is approved
3. **IAM Permissions**: Check that deployment role has sufficient permissions
4. **Region Availability**: Ensure all services are available in selected region

### Logs

Check CloudWatch logs for Lambda functions:
- Upload handler logs: `/aws/lambda/{env}-sow-upload-handler`
- Agent handler logs: `/aws/lambda/{env}-sow-agent-handler`
- Status handler logs: `/aws/lambda/{env}-sow-status-handler`
- List handler logs: `/aws/lambda/{env}-sow-list-handler`

## Contributing

1. Make changes in feature branches
2. Test in development environment first
3. Update documentation as needed
4. Submit pull requests for review

## License

[Add your license information here]
