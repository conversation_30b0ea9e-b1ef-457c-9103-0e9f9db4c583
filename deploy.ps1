#!/usr/bin/env pwsh

<#
.SYNOPSIS
    Deploy SOW Reviewer Infrastructure

.DESCRIPTION
    This script helps deploy the SOW Reviewer infrastructure across different environments
    using Terraform workspaces.

.PARAMETER Environment
    The environment to deploy to (dev, staging, prod)

.PARAMETER Action
    The Terraform action to perform (plan, apply, destroy)

.PARAMETER AutoApprove
    Skip interactive approval for apply and destroy actions

.PARAMETER Profile
    The AWS profile to use (optional, for SSO profiles)

.EXAMPLE
    .\deploy.ps1 -Environment dev -Action plan -Profile ce-test-1
    .\deploy.ps1 -Environment dev -Action apply -Profile ce-test-1
    .\deploy.ps1 -Environment dev -Action apply -Profile ce-test-1 -AutoApprove
#>

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("dev", "staging", "prod")]
    [string]$Environment,
    
    [Parameter(Mandatory=$true)]
    [ValidateSet("plan", "apply", "destroy")]
    [string]$Action,
    
    [Parameter(Mandatory=$false)]
    [string]$Profile,
    
    [Parameter(Mandatory=$false)]
    [switch]$AutoApprove
)

# Color output functions
function Write-Success { param($Message) Write-Host $Message -ForegroundColor Green }
function Write-Error { param($Message) Write-Host $Message -ForegroundColor Red }
function Write-Warning { param($Message) Write-Host $Message -ForegroundColor Yellow }
function Write-Info { param($Message) Write-Host $Message -ForegroundColor Cyan }

# Validate prerequisites
Write-Info "🔍 Checking prerequisites..."

# Check if Terraform is installed
if (!(Get-Command terraform -ErrorAction SilentlyContinue)) {
    Write-Error "❌ Terraform is not installed or not in PATH"
    exit 1
}

# Check if AWS CLI is installed
if (!(Get-Command aws -ErrorAction SilentlyContinue)) {
    Write-Error "❌ AWS CLI is not installed or not in PATH"
    exit 1
}

# Handle AWS Profile and SSO
if ($Profile) {
    Write-Info "🔐 Using AWS profile: $Profile"
    $env:AWS_PROFILE = $Profile
    
    # Check if SSO login is needed
    try {
        $awsIdentity = aws sts get-caller-identity --output json 2>$null | ConvertFrom-Json
        if (!$awsIdentity) {
            throw "No identity"
        }
        Write-Success "✅ AWS SSO session active for account: $($awsIdentity.Account) (Profile: $Profile)"
    } catch {
        Write-Warning "⚠️  AWS SSO session expired or not logged in. Attempting SSO login..."
        Write-Info "🔑 Running 'aws sso login --profile $Profile'..."
        aws sso login --profile $Profile
        if ($LASTEXITCODE -ne 0) {
            Write-Error "❌ AWS SSO login failed"
            exit 1
        }
        
        # Verify login successful
        try {
            $awsIdentity = aws sts get-caller-identity --output json | ConvertFrom-Json
            Write-Success "✅ AWS SSO login successful for account: $($awsIdentity.Account) (Profile: $Profile)"
        } catch {
            Write-Error "❌ AWS credentials still not available after SSO login"
            exit 1
        }
    }
} else {
    # Check for default credentials
    Write-Info "🔐 Checking default AWS credentials..."
    $awsIdentityOutput = aws sts get-caller-identity --output json 2>$null
    if ($LASTEXITCODE -eq 0 -and $awsIdentityOutput) {
        try {
            $awsIdentity = $awsIdentityOutput | ConvertFrom-Json
            Write-Success "✅ AWS credentials configured for account: $($awsIdentity.Account)"
        } catch {
            Write-Error "❌ Error parsing AWS credentials response"
            exit 1
        }
    } else {
        Write-Error "❌ No AWS credentials found. Please specify a profile with -Profile or configure default credentials"
        Write-Info "Available profiles:"
        aws configure list-profiles
        Write-Info "Example: .\deploy.ps1 -Environment dev -Action plan -Profile ce-test-1"
        exit 1
    }
}

# Check if tfvars file exists
$tfvarsFile = "environments/$Environment.tfvars"
if (!(Test-Path $tfvarsFile)) {
    Write-Error "❌ Environment file $tfvarsFile not found"
    exit 1
}

Write-Success "✅ Prerequisites check passed"

# Initialize Terraform if needed
if (!(Test-Path ".terraform")) {
    Write-Info "🚀 Initializing Terraform..."
    terraform init
    if ($LASTEXITCODE -ne 0) {
        Write-Error "❌ Terraform initialization failed"
        exit 1
    }
    Write-Success "✅ Terraform initialized"
}

# Create or select workspace
Write-Info "🔧 Setting up Terraform workspace: $Environment"
$workspaces = terraform workspace list
if ($workspaces -match "\*.*$Environment") {
    Write-Info "📁 Workspace $Environment is already selected"
} elseif ($workspaces -match $Environment) {
    Write-Info "📁 Selecting existing workspace: $Environment"
    terraform workspace select $Environment
    if ($LASTEXITCODE -ne 0) {
        Write-Error "❌ Failed to select workspace"
        exit 1
    }
} else {
    Write-Info "📁 Creating new workspace: $Environment"
    terraform workspace new $Environment
    if ($LASTEXITCODE -ne 0) {
        Write-Error "❌ Failed to create workspace"
        exit 1
    }
}

Write-Success "✅ Workspace $Environment selected"

# Show current workspace
$currentWorkspace = terraform workspace show
Write-Info "📍 Current workspace: $currentWorkspace"

# Execute Terraform command
Write-Info "🚀 Executing Terraform $Action for environment: $Environment"

switch ($Action) {
    "plan" {
        terraform plan -var-file="$tfvarsFile" -out="tfplan-$Environment"
    }
    "apply" {
        if (Test-Path "tfplan-$Environment") {
            # Apply from plan file
            Write-Info "📋 Applying from existing plan file..."
            if ($AutoApprove) {
                terraform apply "tfplan-$Environment"
            } else {
                terraform apply "tfplan-$Environment"
            }
        } else {
            # Apply directly
            if ($AutoApprove) {
                terraform apply -var-file="$tfvarsFile" -auto-approve
            } else {
                terraform apply -var-file="$tfvarsFile"
            }
        }
    }
    "destroy" {
        if ($AutoApprove) {
            terraform destroy -var-file="$tfvarsFile" -auto-approve
        } else {
            terraform destroy -var-file="$tfvarsFile"
        }
    }
}

if ($LASTEXITCODE -ne 0) {
    Write-Error "❌ Terraform $Action failed"
    exit 1
}

Write-Success "✅ Terraform $Action completed successfully"

# Show outputs if apply was successful
if ($Action -eq "apply") {
    Write-Info "📊 Infrastructure outputs:"
    terraform output -json | ConvertFrom-Json | Format-Table -AutoSize
}

Write-Success "🎉 Deployment completed successfully for environment: $Environment"
