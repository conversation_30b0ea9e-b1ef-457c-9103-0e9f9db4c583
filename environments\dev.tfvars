# Environment Configuration for Development
environment = "dev"
region      = "us-east-1"

# Optional: Override foundation model ARN if needed
# foundation_model_arn = "arn:aws:bedrock:us-east-1:123456789012:inference-profile/us.anthropic.claude-sonnet-4-20250514-v1:0"

# Default tags applied to all resources
default_tags = {
  Environment   = "dev"
  Project       = "SOW Reviewer"
  Owner         = "Development Team"
  CostCenter    = "Development"
  Backup        = "false"
  AutoShutdown  = "true"
  CreatedBy     = "Terraform"
}
