import boto3
import urllib.request
import uuid
import json
import os

#3 different clients are needed - ai_client is for a simple API call to a generative AI model, 
#agent_runtime_client is for invoking the Bedrock Agents, and agent_client is for looking up agent info needed for the invocation.
ai_client = boto3.client("bedrock-runtime", region_name="us-east-1")
agent_runtime_client = boto3.client("bedrock-agent-runtime", region_name="us-east-1")
agent_client = boto3.client("bedrock-agent", region_name="us-east-1")

#The handler function expects a url for a SOW.
def lambda_handler(event, context):
    try:
        url = event.get('url')
        if not url:
            return {
                'statusCode': 400,
                'body': json.dumps({'error': 'URL parameter is required'})
            }
        
        result = get_SOW(url)
        return {
            'statusCode': 200,
            'body': json.dumps({'message': result})
        }
    except Exception as e:
        return {
            'statusCode': 500,
            'body': json.dumps({'error': str(e)})
        }

#Retrieves the SOW from the given URL, makes a simple AI call to get the customer name, and extracts all the info.
def get_SOW(url):

    try:
        with urllib.request.urlopen(url) as response:
            file_content = response.read().decode('utf-8')

        # AI call to extract customer name.
        customer_name = extract_customer_name_ai_call(file_content)
        if not customer_name:
            raise Exception("Could not extract customer name from the document.")

        customer_name = customer_name.replace(" ", "_")
        file_content_with_name = f"This SOW is for {customer_name}.\n\n{file_content}"

        technical_review_output = review_tech_gen_ai_call([file_content_with_name])
        financial_review_output = review_funding_gen_ai_call([file_content_with_name])
        legal_review_output = review_legal_gen_ai_call([file_content_with_name])

        # Upload directly to S3 without local files.
        s3 = boto3.client("s3")
        sts = boto3.client('sts')
        
        account_id = sts.get_caller_identity()['Account']
        region = boto3.Session().region_name or 'us-east-1'
        bucket_name = f"{account_id}-{region}-sow-review-bucket"
        prefix = f"{customer_name}/"
        
        reviews = {
            f"{customer_name}_technical_review.txt": technical_review_output,
            f"{customer_name}_financial_review.txt": financial_review_output,
            f"{customer_name}_legal_review.txt": legal_review_output
        }
        
        for filename, content in reviews.items():
            s3.put_object(Bucket=bucket_name, Key=f"{prefix}{filename}", Body=content)

        return f"Review completed. Files uploaded to S3 bucket '{bucket_name}' under prefix '{prefix}'."
    except Exception as e:
        return f"Error processing file: {str(e)}"

    
def extract_customer_name_ai_call(file_content):
    # AI call to extract the customer name from the SOW.
    try:
        conversation = [
            {
                "role": "user",
                "content": [{"text": f"Extract the customer name from the following Statement of Work (SOW) document. Only return the customer name, nothing else.\n\n{file_content}"}]
            }
        ]
        response = ai_client.converse(
            system=[{"text": "You are an assistant that extracts customer names from SOW documents. Only return the customer name, no extra text."}],
            modelId="amazon.nova-lite-v1:0",
            messages=conversation,
        )
        return response['output']['message']['content'][0]['text'].strip()
    except Exception as e:
        return "An error has occurred with the Gen AI call: " + str(e)

#Calls the Technical Review Agent.
def review_tech_gen_ai_call(texts):
    #Agent ID and alias Lookup.
    try:
        agent_ids = agent_client.list_agents()["agentSummaries"]
        for agent in agent_ids:
            if agent["agentName"] == "technical_sow_reviewer_agent":
                agent_id = agent["agentId"]
                aliases = agent_client.list_agent_aliases(agentId=agent_id)
                alias_id = aliases['agentAliasSummaries'][0]['agentAliasId']
                break      
    except Exception as e:
        return "An error has occurred: " + str(e)
    #Invoke the Agent, process response, and return the result.
    try:
        all_text = "\n".join(texts)
        response = agent_runtime_client.invoke_agent(
            agentId=agent_id,
            agentAliasId=alias_id,
            inputText=all_text,
            sessionId=str(uuid.uuid4()),
        )   
    except Exception as e:
        return "An error has occurred with the Gen AI call: " + str(e)
    
    result = ""
    try:
        for event in response['completion']:
            if 'chunk' in event:
                chunk = event['chunk']
                if 'bytes' in chunk:
                    result += chunk['bytes'].decode('utf-8')
    except Exception as e:
        return "An error occurred processing the response stream: " + str(e)

    return result

#Calls the Funding Review Agent.
def review_funding_gen_ai_call(texts):
    #Agent ID and alias Lookup.
    try:
        agent_ids = agent_client.list_agents()["agentSummaries"]
        for agent in agent_ids:
            if agent["agentName"] == "financial_sow_reviewer_agent":
                agent_id = agent["agentId"]
                aliases = agent_client.list_agent_aliases(agentId=agent_id)
                alias_id = aliases['agentAliasSummaries'][0]['agentAliasId']
                break      
    except Exception as e:
        return "An error has occurred: " + str(e)
    
    #Invoke the Agent, process response, and return the result.
    try:
        all_text = "\n".join(texts)
        response = agent_runtime_client.invoke_agent(
            agentId=agent_id,
            agentAliasId=alias_id,
            inputText=all_text,
            sessionId=str(uuid.uuid4()),
        )   
    except Exception as e:
        return "An error has occurred with the Gen AI call: " + str(e)
    
    result = ""
    try:
        for event in response['completion']:
            if 'chunk' in event:
                chunk = event['chunk']
                if 'bytes' in chunk:
                    result += chunk['bytes'].decode('utf-8')
    except Exception as e:
        return "An error occurred processing the response stream: " + str(e)

    return result

#Calls the Legal Review Agent.
def review_legal_gen_ai_call(texts):
    #Agent ID and alias Lookup.
    try:
        agent_ids = agent_client.list_agents()["agentSummaries"]
        for agent in agent_ids:
            if agent["agentName"] == "legal_sow_reviewer_agent":
                agent_id = agent["agentId"]
                aliases = agent_client.list_agent_aliases(agentId=agent_id)
                alias_id = aliases['agentAliasSummaries'][0]['agentAliasId']
                break      
    except Exception as e:
        return "An error has occurred: " + str(e)

    #Invoke the Agent, process response, and return the result.
    try:
        all_text = "\n".join(texts)
        response = agent_runtime_client.invoke_agent(
            agentId=agent_id,
            agentAliasId=alias_id,
            inputText=all_text,
            sessionId=str(uuid.uuid4()),
        )   
    except Exception as e:
        return "An error has occurred with the Gen AI call: " + str(e)
    
    result = ""
    try:
        for event in response['completion']:
            if 'chunk' in event:
                chunk = event['chunk']
                if 'bytes' in chunk:
                    result += chunk['bytes'].decode('utf-8')
    except Exception as e:
        return "An error occurred processing the response stream: " + str(e)

    return result