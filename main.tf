# Get current AWS account and region information
data "aws_caller_identity" "current" {}
data "aws_region" "current" {}

# Local values for common tags and naming
locals {
  common_tags = merge(var.default_tags, {
    Environment   = var.environment
    Project       = "SOW Reviewer"
    ManagedBy     = "Terraform"
    Workspace     = terraform.workspace
  })
  
  # Environment-specific foundation model ARNs
  foundation_model_arns = {
    dev     = "arn:aws:bedrock:us-east-1:${data.aws_caller_identity.current.account_id}:inference-profile/us.anthropic.claude-sonnet-4-********-v1:0"
    staging = "arn:aws:bedrock:us-east-1:${data.aws_caller_identity.current.account_id}:inference-profile/us.anthropic.claude-sonnet-4-********-v1:0"
    prod    = "arn:aws:bedrock:us-east-1:${data.aws_caller_identity.current.account_id}:inference-profile/us.anthropic.claude-sonnet-4-********-v1:0"
  }
  
  # Use provided foundation_model_arn or default based on environment
  foundation_model_arn = var.foundation_model_arn != null ? var.foundation_model_arn : local.foundation_model_arns[var.environment]
}

# S3 Module
module "s3" {
  source = "./modules/s3"
  
  account_id  = data.aws_caller_identity.current.account_id
  region      = var.region
  environment = var.environment
  tags        = local.common_tags
}

# DynamoDB Module
module "dynamodb" {
  source = "./modules/dynamodb"
  
  environment = var.environment
  tags        = local.common_tags
}

# Bedrock Agents Module
module "bedrock_agents" {
  source = "./modules/bedrock-agents"
  
  environment          = var.environment
  region               = var.region
  foundation_model_arn = local.foundation_model_arn
  tags                 = local.common_tags
}

# Lambda Module
module "lambda" {
  source = "./modules/lambda"
  
  environment                   = var.environment
  region                        = var.region
  documents_bucket_name         = module.s3.documents_bucket_name
  documents_bucket_arn          = module.s3.documents_bucket_arn
  reviews_bucket_name           = module.s3.reviews_bucket_name
  reviews_bucket_arn            = module.s3.reviews_bucket_arn
  document_status_table_name    = module.dynamodb.document_status_table_name
  document_status_table_arn     = module.dynamodb.document_status_table_arn
  processing_results_table_name = module.dynamodb.processing_results_table_name
  processing_results_table_arn  = module.dynamodb.processing_results_table_arn
  tags                          = local.common_tags
}

# API Gateway Module
module "api_gateway" {
  source = "./modules/api-gateway"
  
  environment                   = var.environment
  upload_handler_function_name  = module.lambda.upload_handler_function_name
  upload_handler_invoke_arn     = module.lambda.upload_handler_invoke_arn
  agent_handler_function_name   = module.lambda.agent_handler_function_name
  agent_handler_invoke_arn      = module.lambda.agent_handler_invoke_arn
  status_handler_function_name  = module.lambda.status_handler_function_name
  status_handler_invoke_arn     = module.lambda.status_handler_invoke_arn
  list_handler_function_name    = module.lambda.list_handler_function_name
  list_handler_invoke_arn       = module.lambda.list_handler_invoke_arn
  tags                          = local.common_tags
}
