output "api_gateway_id" {
  description = "ID of the API Gateway"
  value       = aws_api_gateway_rest_api.sow_api.id
}

output "api_gateway_url" {
  description = "URL of the API Gateway"
  value       = "https://${aws_api_gateway_rest_api.sow_api.id}.execute-api.${data.aws_region.current.id}.amazonaws.com/${var.environment}"
}

output "api_gateway_stage" {
  description = "Stage of the API Gateway"
  value       = aws_api_gateway_stage.sow_api_stage.stage_name
}

output "upload_endpoint" {
  description = "Upload endpoint URL"
  value       = "https://${aws_api_gateway_rest_api.sow_api.id}.execute-api.${data.aws_region.current.id}.amazonaws.com/${var.environment}/upload"
}

output "process_endpoint" {
  description = "Process endpoint URL"
  value       = "https://${aws_api_gateway_rest_api.sow_api.id}.execute-api.${data.aws_region.current.id}.amazonaws.com/${var.environment}/process"
}

output "status_endpoint" {
  description = "Status endpoint URL"
  value       = "https://${aws_api_gateway_rest_api.sow_api.id}.execute-api.${data.aws_region.current.id}.amazonaws.com/${var.environment}/status"
}

output "documents_endpoint" {
  description = "Documents list endpoint URL"
  value       = "https://${aws_api_gateway_rest_api.sow_api.id}.execute-api.${data.aws_region.current.id}.amazonaws.com/${var.environment}/documents"
}
