variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
}

variable "upload_handler_function_name" {
  description = "Name of the upload handler Lamb<PERSON> function"
  type        = string
}

variable "upload_handler_invoke_arn" {
  description = "Invoke ARN of the upload handler Lamb<PERSON> function"
  type        = string
}

variable "agent_handler_function_name" {
  description = "Name of the agent handler Lambda function"
  type        = string
}

variable "agent_handler_invoke_arn" {
  description = "Invoke ARN of the agent handler Lambda function"
  type        = string
}

variable "status_handler_function_name" {
  description = "Name of the status handler Lambda function"
  type        = string
}

variable "status_handler_invoke_arn" {
  description = "Invoke ARN of the status handler <PERSON><PERSON> function"
  type        = string
}

variable "list_handler_function_name" {
  description = "Name of the list handler Lambda function"
  type        = string
}

variable "list_handler_invoke_arn" {
  description = "Invoke ARN of the list handler Lambda function"
  type        = string
}

variable "tags" {
  description = "Tags to apply to resources"
  type        = map(string)
  default     = {}
}
