data "aws_caller_identity" "current" {}

# IAM permissions for Bedrock agents
data "aws_iam_policy_document" "bedrock_agent_trust_policy" {
  statement {
    sid     = "AmazonBedrockAgentInferenceProfilesCrossRegionPolicyProd"
    effect  = "Allow"
    actions = ["sts:AssumeRole"]
    principals {
      identifiers = ["bedrock.amazonaws.com"]
      type        = "Service"
    }
    condition {
      test     = "StringEquals"
      values   = [data.aws_caller_identity.current.account_id]
      variable = "aws:SourceAccount"
    }
    condition {
      test     = "ArnLike"
      values   = ["arn:aws:bedrock:${var.region}:${data.aws_caller_identity.current.account_id}:agent/*"]
      variable = "aws:SourceArn"
    }
  }
}

data "aws_iam_policy_document" "bedrock_agent_permissions" {
  statement {
    actions = [
      "bedrock:InvokeModel",
      "bedrock:InvokeModelWithResponseStream",
      "bedrock:GetInferenceProfile",
      "bedrock:GetFoundationModel"
    ]
    resources = [
      var.foundation_model_arn,
      "arn:aws:bedrock:${var.region}:${data.aws_caller_identity.current.account_id}:foundation-model/anthropic.claude-sonnet-4-********-v1:0",
      "arn:aws:bedrock:*:${data.aws_caller_identity.current.account_id}:inference-profile/us.anthropic.claude-sonnet-4-********-v1:0",
      "arn:aws:bedrock:*::foundation-model/anthropic.claude-sonnet-4-********-v1:0"
    ]
  }
}
