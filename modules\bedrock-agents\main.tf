# IAM Role for Bedrock Agents
resource "aws_iam_role" "bedrock_agent_role" {
  name_prefix        = "BedrockAgents-${var.environment}-"
  assume_role_policy = data.aws_iam_policy_document.bedrock_agent_trust_policy.json

  tags = var.tags
}

resource "aws_iam_role_policy" "bedrock_agent_policy" {
  name_prefix = "BedrockAgentPolicy_${var.environment}_"
  policy      = data.aws_iam_policy_document.bedrock_agent_permissions.json
  role        = aws_iam_role.bedrock_agent_role.id
}

# Technical Agent
resource "aws_bedrockagent_agent" "technical_agent" {
  agent_name                  = "${var.environment}-technical-sow-reviewer-agent"
  agent_resource_role_arn     = aws_iam_role.bedrock_agent_role.arn
  idle_session_ttl_in_seconds = 500
  foundation_model            = var.foundation_model_arn
  
  depends_on = [aws_iam_role_policy.bedrock_agent_policy]
  instruction = <<-EOT
    You are a project manager for a Cloud Consultation company. You are tasked with reviewing Statement of Work documents from a technical perspective.
    Identify whether there are any portions which may not be feasible, such as overpromising the accuracy of an AI model or on an SLA.
    Identify whether there is any missing language, such as limits or the amount of resources that must be used to meet a certain goal, or any mention of migration without mention of the services needed to setup the new environment.
    Ensure your responses are as accurate as possible and avoid hallucinations.
    The first line of your response should be "Technical Review of Statement of Work For" and then the customer name.
    However, in the rest of the document, the customer should be referred to as 'The Customer.'
  EOT

  tags = var.tags
}

resource "aws_bedrockagent_agent_alias" "technical_agent_alias" {
  agent_alias_name = "${var.environment}-technical-agent-alias"
  agent_id         = aws_bedrockagent_agent.technical_agent.agent_id
  description      = "Technical Agent Alias for ${var.environment}"

  tags = var.tags
}

# Financial Agent
resource "aws_bedrockagent_agent" "financial_agent" {
  agent_name                  = "${var.environment}-financial-sow-reviewer-agent"
  agent_resource_role_arn     = aws_iam_role.bedrock_agent_role.arn
  idle_session_ttl_in_seconds = 500
  foundation_model            = var.foundation_model_arn
  
  depends_on = [aws_iam_role_policy.bedrock_agent_policy]
  instruction = <<-EOT
    You are a project manager for a Cloud Consultation company. You are tasked with reviewing Statement of Work documents from a 
    financial perspective. Identify if all the correct financial verbiage is present. If there are any AWS funding programs mentioned, 
    ensure the Statement of Work accurately reflects any prerequisites to get them, such as MAP requiring we have specific deliverables 
    like the Migration Readiness Assessment. Ensure your responses are as accurate as possible and avoid hallucinations. 
    The first line of your response should be "Financial Review of Statement of Work For" and then the customer name. However, 
    in the rest of the document, the customer should be referred to as 'The Customer.'
  EOT

  tags = var.tags
}

resource "aws_bedrockagent_agent_alias" "financial_agent_alias" {
  agent_alias_name = "${var.environment}-financial-agent-alias"
  agent_id         = aws_bedrockagent_agent.financial_agent.agent_id
  description      = "Financial Agent Alias for ${var.environment}"

  tags = var.tags
}

# Legal Agent
resource "aws_bedrockagent_agent" "legal_agent" {
  agent_name                  = "${var.environment}-legal-sow-reviewer-agent"
  agent_resource_role_arn     = aws_iam_role.bedrock_agent_role.arn
  idle_session_ttl_in_seconds = 500
  foundation_model            = var.foundation_model_arn
  
  depends_on = [aws_iam_role_policy.bedrock_agent_policy]
  instruction = <<-EOT
    You are a project manager for a Cloud Consultation company. You are tasked with reviewing Statement of Work documents from a 
    legal perspective. Identify whether we are using any language that a dishonest customer could use to hold us hostage, get free work 
    or otherwise cause us problems if they really wanted to nitpick. Identify whether we are making any promises with our verbiage 
    that imply we are making promises beyond the scope of the SOW.  Ensure your responses are as accurate as possible and avoid hallucinations. 
    The first line of your response should be "Legal Review of Statement of Work For" and then the customer name. 
    However, in the rest of the document, the customer should be referred to as 'The Customer.'
  EOT

  tags = var.tags
}

resource "aws_bedrockagent_agent_alias" "legal_agent_alias" {
  agent_alias_name = "${var.environment}-legal-agent-alias"
  agent_id         = aws_bedrockagent_agent.legal_agent.agent_id
  description      = "Legal Agent Alias for ${var.environment}"

  tags = var.tags
}
