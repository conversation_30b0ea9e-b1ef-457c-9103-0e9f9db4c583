resource "aws_dynamodb_table" "document_status" {
  name           = "${var.environment}-sow-document-status"
  billing_mode   = "PAY_PER_REQUEST"
  hash_key       = "document_id"
  
  attribute {
    name = "document_id"
    type = "S"
  }

  attribute {
    name = "status"
    type = "S"
  }

  attribute {
    name = "created_at"
    type = "S"
  }

  global_secondary_index {
    name            = "StatusIndex"
    hash_key        = "status"
    range_key       = "created_at"
    projection_type = "ALL"
  }

  tags = merge(var.tags, {
    Name = "${var.environment}-sow-document-status"
  })
}

resource "aws_dynamodb_table" "processing_results" {
  name           = "${var.environment}-sow-processing-results"
  billing_mode   = "PAY_PER_REQUEST"
  hash_key       = "document_id"
  range_key      = "agent_type"
  
  attribute {
    name = "document_id"
    type = "S"
  }

  attribute {
    name = "agent_type"
    type = "S"
  }

  tags = merge(var.tags, {
    Name = "${var.environment}-sow-processing-results"
  })
}
