output "document_status_table_name" {
  description = "Name of the document status DynamoDB table"
  value       = aws_dynamodb_table.document_status.name
}

output "document_status_table_arn" {
  description = "ARN of the document status DynamoDB table"
  value       = aws_dynamodb_table.document_status.arn
}

output "processing_results_table_name" {
  description = "Name of the processing results DynamoDB table"
  value       = aws_dynamodb_table.processing_results.name
}

output "processing_results_table_arn" {
  description = "ARN of the processing results DynamoDB table"
  value       = aws_dynamodb_table.processing_results.arn
}
