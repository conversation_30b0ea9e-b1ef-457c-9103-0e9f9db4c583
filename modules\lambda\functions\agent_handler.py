import json
import boto3
import uuid
from datetime import datetime, timezone
import os

def lambda_handler(event, context):
    try:
        # Extract parameters from event
        body = json.loads(event['body']) if isinstance(event.get('body'), str) else event.get('body', {})
        
        document_id = body.get('document_id')
        agent_type = body.get('agent_type')  # 'technical', 'financial', or 'legal'
        
        if not document_id or not agent_type:
            return {
                'statusCode': 400,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Headers': 'Content-Type',
                    'Access-Control-Allow-Methods': 'POST'
                },
                'body': json.dumps({'error': 'document_id and agent_type are required'})
            }
        
        if agent_type not in ['technical', 'financial', 'legal']:
            return {
                'statusCode': 400,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Headers': 'Content-Type',
                    'Access-Control-Allow-Methods': 'POST'
                },
                'body': json.dumps({'error': 'agent_type must be technical, financial, or legal'})
            }
        
        # Get document from DynamoDB
        dynamodb = boto3.resource('dynamodb')
        doc_table = dynamodb.Table(os.environ['DOCUMENT_STATUS_TABLE'])
        
        response = doc_table.get_item(Key={'document_id': document_id})
        if 'Item' not in response:
            return {
                'statusCode': 404,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Headers': 'Content-Type',
                    'Access-Control-Allow-Methods': 'POST'
                },
                'body': json.dumps({'error': 'Document not found'})
            }
        
        document = response['Item']
        s3_uri = document['s3_uri']
        
        # Download document from S3
        s3_client = boto3.client('s3')
        bucket_name, s3_key = s3_uri.replace('s3://', '').split('/', 1)
        
        response = s3_client.get_object(Bucket=bucket_name, Key=s3_key)
        file_content = response['Body'].read().decode('utf-8')
        
        # Extract customer name using AI
        customer_name = extract_customer_name_ai_call(file_content)
        if not customer_name:
            customer_name = "Unknown_Customer"
        
        customer_name = customer_name.replace(" ", "_")
        file_content_with_name = f"This SOW is for {customer_name}.\n\n{file_content}"
        
        # Invoke appropriate agent
        review_output = invoke_agent(agent_type, file_content_with_name)
        
        # Save result to DynamoDB
        results_table = dynamodb.Table(os.environ['PROCESSING_RESULTS_TABLE'])
        current_time = datetime.now(timezone.utc).isoformat()
        
        results_table.put_item(
            Item={
                'document_id': document_id,
                'agent_type': agent_type,
                'customer_name': customer_name,
                'review_content': review_output,
                'created_at': current_time
            }
        )
        
        # Save to S3 reviews bucket
        reviews_bucket = os.environ['REVIEWS_BUCKET_NAME']
        review_key = f"{customer_name}/{customer_name}_{agent_type}_review.txt"
        
        s3_client.put_object(
            Bucket=reviews_bucket,
            Key=review_key,
            Body=review_output,
            ContentType='text/plain'
        )
        
        # Update document status
        doc_table.update_item(
            Key={'document_id': document_id},
            UpdateExpression='SET updated_at = :time, #status = :status',
            ExpressionAttributeNames={'#status': 'status'},
            ExpressionAttributeValues={
                ':time': current_time,
                ':status': f'processed_{agent_type}'
            }
        )
        
        return {
            'statusCode': 200,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type',
                'Access-Control-Allow-Methods': 'POST'
            },
            'body': json.dumps({
                'document_id': document_id,
                'agent_type': agent_type,
                'customer_name': customer_name,
                'review_s3_uri': f"s3://{reviews_bucket}/{review_key}",
                'message': f'{agent_type.title()} review completed successfully'
            })
        }
        
    except Exception as e:
        print(f"Error: {str(e)}")
        return {
            'statusCode': 500,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type',
                'Access-Control-Allow-Methods': 'POST'
            },
            'body': json.dumps({'error': f'Internal server error: {str(e)}'})
        }

def extract_customer_name_ai_call(file_content):
    """Extract customer name using AI."""
    try:
        region = os.environ.get('AWS_REGION', 'us-east-1')
        ai_client = boto3.client("bedrock-runtime", region_name=region)
        conversation = [
            {
                "role": "user",
                "content": [{"text": f"Extract the customer name from the following Statement of Work (SOW) document. Only return the customer name, nothing else.\n\n{file_content}"}]
            }
        ]
        response = ai_client.converse(
            system=[{"text": "You are an assistant that extracts customer names from SOW documents. Only return the customer name, no extra text."}],
            modelId="amazon.nova-lite-v1:0",
            messages=conversation,
        )
        return response['output']['message']['content'][0]['text'].strip()
    except Exception as e:
        print(f"Error extracting customer name: {str(e)}")
        return None

def invoke_agent(agent_type, file_content):
    """Invoke the specified agent type."""
    try:
        region = os.environ.get('AWS_REGION', 'us-east-1')
        agent_runtime_client = boto3.client("bedrock-agent-runtime", region_name=region)
        agent_client = boto3.client("bedrock-agent", region_name=region)
        
        # Map agent types to agent names (based on environment)
        environment = os.environ['ENVIRONMENT']
        agent_name_map = {
            'technical': f"{environment}-technical-sow-reviewer-agent",
            'financial': f"{environment}-financial-sow-reviewer-agent",
            'legal': f"{environment}-legal-sow-reviewer-agent"
        }
        
        agent_name = agent_name_map[agent_type]
        
        # Find agent ID and alias
        agents = agent_client.list_agents()["agentSummaries"]
        agent_id = None
        for agent in agents:
            if agent["agentName"] == agent_name:
                agent_id = agent["agentId"]
                break
        
        if not agent_id:
            raise Exception(f"Agent {agent_name} not found")
        
        aliases = agent_client.list_agent_aliases(agentId=agent_id)
        alias_id = aliases['agentAliasSummaries'][0]['agentAliasId']
        
        # Invoke agent
        response = agent_runtime_client.invoke_agent(
            agentId=agent_id,
            agentAliasId=alias_id,
            inputText=file_content,
            sessionId=str(uuid.uuid4()),
        )
        
        # Process response
        result = ""
        for event in response['completion']:
            if 'chunk' in event:
                chunk = event['chunk']
                if 'bytes' in chunk:
                    result += chunk['bytes'].decode('utf-8')
        
        return result
        
    except Exception as e:
        return f"Error invoking {agent_type} agent: {str(e)}"
