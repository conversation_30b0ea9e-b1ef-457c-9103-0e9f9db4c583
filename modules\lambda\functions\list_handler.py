import json
import boto3
from boto3.dynamodb.conditions import Key
import os

def lambda_handler(event, context):
    try:
        # Get optional query parameters
        query_params = event.get('queryStringParameters') or {}
        status = query_params.get('status', 'uploaded')  # Default to 'uploaded' status
        limit = int(query_params.get('limit', 50))  # Default limit of 50
        
        # Get documents from DynamoDB using GSI
        dynamodb = boto3.resource('dynamodb')
        table = dynamodb.Table(os.environ['DOCUMENT_STATUS_TABLE'])
        
        response = table.query(
            IndexName='StatusIndex',
            KeyConditionExpression=Key('status').eq(status),
            Limit=limit,
            ScanIndexForward=False  # Most recent first
        )
        
        documents = []
        for item in response.get('Items', []):
            documents.append({
                'document_id': item['document_id'],
                'file_name': item['file_name'],
                'status': item['status'],
                's3_uri': item['s3_uri'],
                'created_at': item['created_at'],
                'updated_at': item['updated_at']
            })
        
        return {
            'statusCode': 200,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type',
                'Access-Control-Allow-Methods': 'GET'
            },
            'body': json.dumps({
                'documents': documents,
                'count': len(documents),
                'status_filter': status
            })
        }
        
    except Exception as e:
        print(f"Error: {str(e)}")
        return {
            'statusCode': 500,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type',
                'Access-Control-Allow-Methods': 'GET'
            },
            'body': json.dumps({'error': f'Internal server error: {str(e)}'})
        }
