import json
import boto3
from boto3.dynamodb.conditions import Key
import os

def lambda_handler(event, context):
    try:
        # Get document_id from path parameters
        document_id = event.get('pathParameters', {}).get('document_id')
        
        if not document_id:
            return {
                'statusCode': 400,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Headers': 'Content-Type',
                    'Access-Control-Allow-Methods': 'GET'
                },
                'body': json.dumps({'error': 'document_id is required'})
            }
        
        # Get document status from DynamoDB
        dynamodb = boto3.resource('dynamodb')
        doc_table = dynamodb.Table(os.environ['DOCUMENT_STATUS_TABLE'])
        results_table = dynamodb.Table(os.environ['PROCESSING_RESULTS_TABLE'])
        
        # Get document info
        response = doc_table.get_item(Key={'document_id': document_id})
        if 'Item' not in response:
            return {
                'statusCode': 404,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Headers': 'Content-Type',
                    'Access-Control-Allow-Methods': 'GET'
                },
                'body': json.dumps({'error': 'Document not found'})
            }
        
        document = response['Item']
        
        # Get processing results
        results_response = results_table.query(
            KeyConditionExpression=Key('document_id').eq(document_id)
        )
        
        processing_results = []
        for item in results_response.get('Items', []):
            processing_results.append({
                'agent_type': item['agent_type'],
                'customer_name': item.get('customer_name'),
                'created_at': item['created_at'],
                'review_available': True
            })
        
        return {
            'statusCode': 200,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type',
                'Access-Control-Allow-Methods': 'GET'
            },
            'body': json.dumps({
                'document_id': document_id,
                'status': document['status'],
                'file_name': document['file_name'],
                's3_uri': document['s3_uri'],
                'created_at': document['created_at'],
                'updated_at': document['updated_at'],
                'processing_results': processing_results
            })
        }
        
    except Exception as e:
        print(f"Error: {str(e)}")
        return {
            'statusCode': 500,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type',
                'Access-Control-Allow-Methods': 'GET'
            },
            'body': json.dumps({'error': f'Internal server error: {str(e)}'})
        }
