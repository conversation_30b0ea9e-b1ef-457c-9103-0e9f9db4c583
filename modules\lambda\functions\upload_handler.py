import json
import boto3
import uuid
import base64
from datetime import datetime
import os

def lambda_handler(event, context):
    try:
        # Extract file data from event
        body = json.loads(event['body']) if isinstance(event.get('body'), str) else event.get('body', {})
        
        file_content = body.get('file_content')  # Base64 encoded file content
        file_name = body.get('file_name')
        content_type = body.get('content_type', 'application/octet-stream')
        
        if not file_content or not file_name:
            return {
                'statusCode': 400,
                'headers': {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Headers': 'Content-Type',
                    'Access-Control-Allow-Methods': 'POST'
                },
                'body': json.dumps({'error': 'file_content and file_name are required'})
            }
        
        # Generate unique document ID
        document_id = str(uuid.uuid4())
        
        # Decode and upload to S3
        s3_client = boto3.client('s3')
        file_data = base64.b64decode(file_content)
        
        bucket_name = os.environ['DOCUMENTS_BUCKET_NAME']
        s3_key = f"documents/{document_id}/{file_name}"
        
        s3_client.put_object(
            Bucket=bucket_name,
            Key=s3_key,
            Body=file_data,
            ContentType=content_type
        )
        
        s3_uri = f"s3://{bucket_name}/{s3_key}"
        
        # Update DynamoDB with document status
        dynamodb = boto3.resource('dynamodb')
        table = dynamodb.Table(os.environ['DOCUMENT_STATUS_TABLE'])
        
        current_time = datetime.utcnow().isoformat()
        
        table.put_item(
            Item={
                'document_id': document_id,
                'status': 'uploaded',
                'file_name': file_name,
                's3_uri': s3_uri,
                'content_type': content_type,
                'created_at': current_time,
                'updated_at': current_time
            }
        )
        
        return {
            'statusCode': 200,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type',
                'Access-Control-Allow-Methods': 'POST'
            },
            'body': json.dumps({
                'document_id': document_id,
                's3_uri': s3_uri,
                'message': 'File uploaded successfully'
            })
        }
        
    except Exception as e:
        print(f"Error: {str(e)}")
        return {
            'statusCode': 500,
            'headers': {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type',
                'Access-Control-Allow-Methods': 'POST'
            },
            'body': json.dumps({'error': f'Internal server error: {str(e)}'})
        }
