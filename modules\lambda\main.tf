# Create ZIP files for each Lambda function
data "archive_file" "upload_handler" {
  type        = "zip"
  source_file = "${path.module}/functions/upload_handler.py"
  output_path = "${path.module}/upload_handler.zip"
}

data "archive_file" "agent_handler" {
  type        = "zip"
  source_file = "${path.module}/functions/agent_handler.py"
  output_path = "${path.module}/agent_handler.zip"
}

data "archive_file" "status_handler" {
  type        = "zip"
  source_file = "${path.module}/functions/status_handler.py"
  output_path = "${path.module}/status_handler.zip"
}

data "archive_file" "list_handler" {
  type        = "zip"
  source_file = "${path.module}/functions/list_handler.py"
  output_path = "${path.module}/list_handler.zip"
}

# IAM Role for Lambda functions
resource "aws_iam_role" "lambda_role" {
  name_prefix = "${var.environment}-sow-lambda-role-"
  assume_role_policy = data.aws_iam_policy_document.lambda_assume_role.json

  tags = var.tags
}

resource "aws_iam_role_policy" "lambda_policy" {
  name_prefix = "${var.environment}-sow-lambda-policy-"
  policy = data.aws_iam_policy_document.lambda_permissions.json
  role   = aws_iam_role.lambda_role.id
}

resource "aws_iam_role_policy_attachment" "lambda_basic_execution" {
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
  role       = aws_iam_role.lambda_role.name
}

# Upload Handler Lambda
resource "aws_lambda_function" "upload_handler" {
  filename         = data.archive_file.upload_handler.output_path
  function_name    = "${var.environment}-sow-upload-handler"
  role            = aws_iam_role.lambda_role.arn
  handler         = "upload_handler.lambda_handler"
  source_code_hash = data.archive_file.upload_handler.output_base64sha256
  timeout         = 60
  memory_size     = 256
  runtime         = "python3.11"

  environment {
    variables = {
      DOCUMENTS_BUCKET_NAME = var.documents_bucket_name
      DOCUMENT_STATUS_TABLE = var.document_status_table_name
      ENVIRONMENT          = var.environment
    }
  }

  tags = var.tags
}

# Agent Handler Lambda
resource "aws_lambda_function" "agent_handler" {
  filename         = data.archive_file.agent_handler.output_path
  function_name    = "${var.environment}-sow-agent-handler"
  role            = aws_iam_role.lambda_role.arn
  handler         = "agent_handler.lambda_handler"
  source_code_hash = data.archive_file.agent_handler.output_base64sha256
  timeout         = 900
  memory_size     = 1024
  runtime         = "python3.11"

  environment {
    variables = {
      DOCUMENTS_BUCKET_NAME      = var.documents_bucket_name
      REVIEWS_BUCKET_NAME        = var.reviews_bucket_name
      DOCUMENT_STATUS_TABLE      = var.document_status_table_name
      PROCESSING_RESULTS_TABLE   = var.processing_results_table_name
      ENVIRONMENT               = var.environment
    }
  }

  tags = var.tags
}

# Status Handler Lambda
resource "aws_lambda_function" "status_handler" {
  filename         = data.archive_file.status_handler.output_path
  function_name    = "${var.environment}-sow-status-handler"
  role            = aws_iam_role.lambda_role.arn
  handler         = "status_handler.lambda_handler"
  source_code_hash = data.archive_file.status_handler.output_base64sha256
  timeout         = 30
  memory_size     = 256
  runtime         = "python3.11"

  environment {
    variables = {
      DOCUMENT_STATUS_TABLE    = var.document_status_table_name
      PROCESSING_RESULTS_TABLE = var.processing_results_table_name
      ENVIRONMENT             = var.environment
    }
  }

  tags = var.tags
}

# List Handler Lambda
resource "aws_lambda_function" "list_handler" {
  filename         = data.archive_file.list_handler.output_path
  function_name    = "${var.environment}-sow-list-handler"
  role            = aws_iam_role.lambda_role.arn
  handler         = "list_handler.lambda_handler"
  source_code_hash = data.archive_file.list_handler.output_base64sha256
  timeout         = 30
  memory_size     = 256
  runtime         = "python3.11"

  environment {
    variables = {
      DOCUMENT_STATUS_TABLE = var.document_status_table_name
      ENVIRONMENT          = var.environment
    }
  }

  tags = var.tags
}
