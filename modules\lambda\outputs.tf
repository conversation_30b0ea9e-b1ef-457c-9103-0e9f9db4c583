output "upload_handler_function_name" {
  description = "Name of the upload handler <PERSON><PERSON> function"
  value       = aws_lambda_function.upload_handler.function_name
}

output "upload_handler_function_arn" {
  description = "ARN of the upload handler <PERSON><PERSON> function"
  value       = aws_lambda_function.upload_handler.arn
}

output "upload_handler_invoke_arn" {
  description = "Invoke ARN of the upload handler Lambda function"
  value       = aws_lambda_function.upload_handler.invoke_arn
}

output "agent_handler_function_name" {
  description = "Name of the agent handler Lambda function"
  value       = aws_lambda_function.agent_handler.function_name
}

output "agent_handler_function_arn" {
  description = "ARN of the agent handler Lambda function"
  value       = aws_lambda_function.agent_handler.arn
}

output "agent_handler_invoke_arn" {
  description = "Invoke ARN of the agent handler Lambda function"
  value       = aws_lambda_function.agent_handler.invoke_arn
}

output "status_handler_function_name" {
  description = "Name of the status handler Lamb<PERSON> function"
  value       = aws_lambda_function.status_handler.function_name
}

output "status_handler_function_arn" {
  description = "ARN of the status handler Lambda function"
  value       = aws_lambda_function.status_handler.arn
}

output "status_handler_invoke_arn" {
  description = "Invoke ARN of the status handler Lambda function"
  value       = aws_lambda_function.status_handler.invoke_arn
}

output "list_handler_function_name" {
  description = "Name of the list handler Lambda function"
  value       = aws_lambda_function.list_handler.function_name
}

output "list_handler_function_arn" {
  description = "ARN of the list handler Lambda function"
  value       = aws_lambda_function.list_handler.arn
}

output "list_handler_invoke_arn" {
  description = "Invoke ARN of the list handler Lambda function"
  value       = aws_lambda_function.list_handler.invoke_arn
}
