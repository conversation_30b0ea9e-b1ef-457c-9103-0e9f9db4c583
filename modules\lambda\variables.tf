variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
}

variable "region" {
  description = "AWS region"
  type        = string
}

variable "documents_bucket_name" {
  description = "Name of the S3 bucket for documents"
  type        = string
}

variable "documents_bucket_arn" {
  description = "ARN of the S3 bucket for documents"
  type        = string
}

variable "reviews_bucket_name" {
  description = "Name of the S3 bucket for reviews"
  type        = string
}

variable "reviews_bucket_arn" {
  description = "ARN of the S3 bucket for reviews"
  type        = string
}

variable "document_status_table_name" {
  description = "Name of the document status DynamoDB table"
  type        = string
}

variable "document_status_table_arn" {
  description = "ARN of the document status DynamoDB table"
  type        = string
}

variable "processing_results_table_name" {
  description = "Name of the processing results DynamoDB table"
  type        = string
}

variable "processing_results_table_arn" {
  description = "ARN of the processing results DynamoDB table"
  type        = string
}

variable "tags" {
  description = "Tags to apply to resources"
  type        = map(string)
  default     = {}
}
