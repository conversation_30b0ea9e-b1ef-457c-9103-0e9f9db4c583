resource "aws_s3_bucket" "sow_documents_bucket" {
  bucket = "${var.account_id}-${var.region}-${var.environment}-sow-documents-bucket"
}

resource "aws_s3_bucket_versioning" "sow_documents_bucket_versioning" {
  bucket = aws_s3_bucket.sow_documents_bucket.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "sow_documents_bucket_encryption" {
  bucket = aws_s3_bucket.sow_documents_bucket.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

resource "aws_s3_bucket_cors_configuration" "sow_documents_bucket_cors" {
  bucket = aws_s3_bucket.sow_documents_bucket.id

  cors_rule {
    allowed_headers = ["*"]
    allowed_methods = ["PUT", "POST", "GET"]
    allowed_origins = ["*"]
    expose_headers  = ["ETag"]
    max_age_seconds = 3000
  }
}

resource "aws_s3_bucket" "sow_reviews_bucket" {
  bucket = "${var.account_id}-${var.region}-${var.environment}-sow-reviews-bucket"
}

resource "aws_s3_bucket_versioning" "sow_reviews_bucket_versioning" {
  bucket = aws_s3_bucket.sow_reviews_bucket.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "sow_reviews_bucket_encryption" {
  bucket = aws_s3_bucket.sow_reviews_bucket.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}
